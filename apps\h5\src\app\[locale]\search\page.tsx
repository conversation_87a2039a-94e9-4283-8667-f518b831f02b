'use client'
import { useCallback, useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useTranslations } from 'next-intl'
import { appLocalStorage, ROUTE, storeConfigSelector, useSearchHotWordsQuery } from '@ninebot/core'
import { useVolcAnalytics } from '@ninebot/core/src/businessHooks'
import { useNavigate } from '@ninebot/core/src/businessHooks'
import { TRACK_EVENT } from '@ninebot/core/src/constants'
import { GetSearchHotWordsQuery } from '@ninebot/core/src/graphql/generated/graphql'
import usePagination from '@ninebot/core/src/hooks/usePagination'
import { useAppSelector } from '@ninebot/core/src/store/hooks'
import { Dialog } from 'antd-mobile'
import clsx from 'clsx'

import { CustomSearchBar, RecommendedProducts, Skeleton } from '@/components'
import { Change, IconDelete, Trash } from '@/components/icons'
import { commonStyles } from '@/constants'

// 添加常量
const SEARCH_HISTORY_KEY = 'search_history'

type HotSearchList = NonNullable<NonNullable<GetSearchHotWordsQuery['search_hot_words']>>['items']

export default function SearchPage() {
  const router = useRouter()
  const getI18nString = useTranslations('Common')
  const { openPage } = useNavigate()
  const storeConfig = useAppSelector(storeConfigSelector)
  const defaultSearchWord = storeConfig?.default_search_words || ''
  const { reportEvent } = useVolcAnalytics()

  const [isLoading, setIsLoading] = useState(true)
  const [isRotating, setIsRotating] = useState(false)
  const [searchHistory, setSearchHistory] = useState<string[]>([])
  const [searchValue, setSearchValue] = useState('')
  const [totalPages, setTotalPages] = useState(1)
  const [hotSearchList, setHotSearchList] = useState<HotSearchList>([])
  const [isDelete, setIsDelete] = useState(false)
  // const [isExpanded, setIsExpanded] = useState(false)
  const [canShow, setCanShow] = useState(true)

  const { page, pageSize, handlePageChange, handlePageNext } = usePagination(1, 6)

  const { data: hotSearchData, isFetching } = useSearchHotWordsQuery({
    currentPage: page,
    pageSize,
  })

  useEffect(() => {
    if (hotSearchData?.search_hot_words?.items?.length) {
      setHotSearchList(hotSearchData?.search_hot_words?.items)
      setTotalPages(hotSearchData?.search_hot_words?.page_info?.total_pages as number)
    }
  }, [hotSearchData])

  // 埋点：搜索页曝光
  useEffect(() => {
    reportEvent(TRACK_EVENT.shop_search_exposure)
  }, [reportEvent])

  const handleRefreshClick = () => {
    reportEvent(TRACK_EVENT.shop_searchpage_switch_products_click, {
      button_id: 'shop_change_trending keywords',
    })

    if (page < totalPages) {
      handlePageNext()
    } else {
      handlePageChange(1)
    }
  }

  const getSearchHistory = useCallback(async () => {
    const history = (await appLocalStorage.getItem(SEARCH_HISTORY_KEY)) || '[]'
    if (history) {
      setSearchHistory(JSON.parse(history as string))
    }
  }, [])

  // 在组件内添加状态
  useEffect(() => {
    // 从localStorage加载搜索历史
    getSearchHistory()
  }, [getSearchHistory])

  const handleClear = () => {
    // setIsExpanded(false)
  }

  // 删除单个搜索历史
  const handleItemDelete = async (value: string) => {
    handleClear()
    const newSearchHistoryList = searchHistory.filter((item) => item !== value)
    setSearchHistory(newSearchHistoryList)
    await appLocalStorage.setItem(SEARCH_HISTORY_KEY, JSON.stringify(newSearchHistoryList))
  }

  // 清空功能
  const clearSearchHistory = () => {
    Dialog.confirm({
      bodyClassName: 'custom-dialog-confirm',
      content: getI18nString('confirm_delete_history'),
      cancelText: getI18nString('thinking'),
      confirmText: getI18nString('confirm'),
      onConfirm: async () => {
        await appLocalStorage.removeItem(SEARCH_HISTORY_KEY)
        setSearchHistory([])
      },
    })
  }

  // 切换热搜组
  const toggleHotSearches = () => {
    if (isFetching) {
      return
    }

    // 埋点：换一换点击
    reportEvent(TRACK_EVENT.shop_searchpage_switch_products_click)

    handleRefreshClick()
    setIsRotating(true)
    // 动画结束后重置状态
    setTimeout(() => {
      setIsRotating(false)
    }, 300) // 与CSS动画时长保持一致
  }

  // 添加搜索建议处理函数
  const handleSearch = async (value: string) => {
    setSearchValue(value)
    await updateSearchHistory(value)
  }

  const handleClick = async (value: string) => {
    if (!value) return
    await updateSearchHistory(value)

    reportEvent(TRACK_EVENT.shop_searchpage_product_name_click, {
      product_id: '',
      product_name: value,
    })

    openPage({
      route: ROUTE.searchResult,
      value,
    })
  }

  // 添加搜索历史更新函数
  const updateSearchHistory = async (keyword: string) => {
    if (!keyword.trim()) return

    const newHistory = [keyword, ...searchHistory.filter((item) => item !== keyword)]

    setSearchHistory(newHistory)
    await appLocalStorage.setItem(SEARCH_HISTORY_KEY, JSON.stringify(newHistory))
  }

  useEffect(() => {
    if (isFetching) {
      setIsLoading(true)
    } else {
      const timeoutId = setTimeout(() => {
        setIsLoading(false)
      }, 15)

      reportEvent(TRACK_EVENT.shop_search_exposure)

      return () => clearTimeout(timeoutId)
    }
  }, [isFetching, reportEvent])

  return (
    <>
      <div className="min-h-screen bg-white px-base-16">
        <div className="mb-[8px]">
          <CustomSearchBar
            searchValue={searchValue}
            setSearchValue={setSearchValue}
            onSearch={handleSearch}
            onCancel={() => router.back()}
            setCanShow={setCanShow}
            placeholder={defaultSearchWord}
          />
        </div>

        {/* 当没有输入内容时显示历史记录、热搜和推荐产品 */}
        {canShow ? (
          <>
            {/* 搜索历史 */}
            {searchHistory.length > 0 && (
              <div className="mb-[36px]">
                <div className="mb-base-16 flex items-center justify-between">
                  <div className="font-miSansDemiBold450 text-[18px] leading-[22px] text-[#000000]">
                    {getI18nString('search_history')}
                  </div>
                  {isDelete ? (
                    <div className={commonStyles.flex_center}>
                      <div onClick={clearSearchHistory}>
                        <div className="font-miSansMedium380 text-[14px] leading-[1.2] text-[#6E6E73]">
                          {getI18nString('clear')}
                        </div>
                      </div>
                      <div className="mx-4 h-[13px] w-[1px] bg-[#86868B]" />
                      <div
                        onClick={() => {
                          setIsDelete(false)
                          handleClear()
                        }}>
                        <div className="font-miSansMedium380 text-[14px] leading-[1.2] text-[#DA291C]">
                          {getI18nString('done')}
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className={commonStyles.flex_center}>
                      <div
                        onClick={() => {
                          setIsDelete(true)
                          handleClear()
                        }}>
                        <Trash />
                      </div>
                      {/* {searchHistory.length > 4 && (
                        <>
                          <div className="mx-4 h-[13px] w-[1px] bg-[#6E6E73]" />
                          <div
                            className="flex h-8 flex-row items-center justify-center rounded-[100px] pr-4"
                            onClick={() => setIsExpanded(!isExpanded)}>
                            <Arrow color="#86868B" size={18} rotate={isExpanded ? -90 : 90} />
                          </div>
                        </>
                      )} */}
                    </div>
                  )}
                </div>
                <div
                  className="flex flex-wrap gap-base overflow-clip"
                  // style={{
                  //   maxHeight: isExpanded || isDelete ? 250 : 74,
                  // }}
                >
                  {searchHistory.slice(0, 10).map((item, index) => (
                    <div
                      key={index}
                      className={clsx(
                        'rounded-[10rem] bg-[#F3F3F4] px-base-16 py-base font-miSansDemiBold450 text-[12px] leading-[1.2] text-[#000000]',
                        commonStyles.flex_row,
                      )}>
                      <button
                        key={item}
                        className="line-clamp-1 truncate"
                        onClick={() => handleClick(item)}>
                        {item}
                      </button>
                      {isDelete && (
                        <div className="ml-[8px]" onClick={() => handleItemDelete(item)}>
                          <IconDelete />
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* 热搜区域 */}
            {hotSearchList?.length ? (
              isLoading ? (
                <div className="mb-0 h-[146px]">
                  <div className="mb-[8px] flex flex-row items-center justify-between">
                    <Skeleton
                      style={{
                        height: 26,
                        width: 88,
                        borderRadius: 4,
                      }}
                    />
                    <Skeleton
                      style={{
                        height: 26,
                        width: 70,
                        borderRadius: 4,
                      }}
                    />
                  </div>
                  <div className="flex flex-wrap justify-between">
                    {Array.from({ length: 6 }, (_, index) => ({ id: index })).map((item) => (
                      <Skeleton
                        key={item.id}
                        style={{
                          borderRadius: 4,
                          height: 32,
                          width: 166,
                          marginBottom: 12,
                        }}
                      />
                    ))}
                  </div>
                </div>
              ) : (
                <div className="mb-[36px]">
                  <div className="mb-base-16 flex items-center justify-between">
                    <span className="font-miSansDemiBold450 text-[18px] leading-[1.2] text-[#000000]">
                      {getI18nString('ninebot_hot_search')}
                    </span>
                    <button
                      onClick={toggleHotSearches}
                      className="flex items-center text-gray-3 hover:text-gray-700">
                      <Change
                        className={`transition-transform duration-300 ${isRotating ? 'rotate-180' : ''}`}
                      />
                      <span className="ml-1 text-base">{getI18nString('refresh')}</span>
                    </button>
                  </div>
                  <div className="grid grid-cols-2 gap-x-[11px] gap-y-[6px]">
                    {hotSearchList.map((item, index) => (
                      <button
                        key={index}
                        onClick={() => handleClick(item?.words || '')}
                        className="group flex h-base-32 items-center">
                        <span className="truncate text-[14px] leading-[1.2] text-[#000000]">
                          {item?.words}
                        </span>
                        {item?.tag && (
                          <span
                            className={`ml-[6px] truncate rounded-[20px] px-[6px] py-[4px] text-[10px] leading-[12px] text-white`}
                            style={{
                              backgroundColor: item?.color || '#DA291C',
                            }}>
                            {item.tag}
                          </span>
                        )}
                      </button>
                    ))}
                  </div>
                </div>
              )
            ) : null}

            {/* 猜你喜欢 */}
            <RecommendedProducts pageType="search" />
          </>
        ) : null}
      </div>
    </>
  )
}
