import { Metadata } from 'next'
import { getTranslations } from 'next-intl/server'

/**
 * 生成元数据
 */
export async function generateMetadata({
  params,
}: {
  params: { locale: string }
}): Promise<Metadata> {
  const { locale } = params
  const t = await getTranslations({ locale, namespace: 'Metadata' })

  return {
    title: {
      absolute: t('login_callback'),
    },
    keywords: t('login_callback'),
    description: t('login_callback'),
  }
}

export default function Layout({ children }: { children: React.ReactNode }) {
  return <>{children}</>
}
