import { Fragment, useState } from 'react'
import { RenderHtml, useGetCmsBlockQuery } from '@ninebot/core'

import { CustomPopup } from '@/components'
import { Arrow, Check } from '@/components/icons'

import { useProduct } from '../../context/ProductContext'

interface SafeguardItem {
  label: string
  value: string
}

// interface CmsBlock {
//   title: string
//   content: string
// }

const SafeguardClauses = () => {
  const [serviceVisible, setServiceVisible] = useState(false)
  const { safeguardItems } = useProduct() as { safeguardItems: SafeguardItem[] }
  const safeguardIds = safeguardItems.map((item: SafeguardItem) => item.value)
  const { currentData: safeguardData, isFetching } = useGetCmsBlockQuery({
    identifiers: safeguardIds,
  })

  const safeguardContent = safeguardData ? safeguardData?.cmsBlocks?.items : []

  if (isFetching) {
    return 'loading...'
  }

  if (safeguardContent?.length === 0) {
    return null
  }

  return (
    <>
      <div className="flex items-center gap-base-24">
        <span className="font-miSansRegular330 text-[#222223]">保障</span>
        <div
          className="flex flex-1 items-center justify-between gap-base overflow-hidden"
          onClick={() => setServiceVisible(true)}>
          <div className="flex-1 truncate font-miSansRegular330">
            {safeguardItems.map((clause: SafeguardItem, index: number) => (
              <Fragment key={index}>
                <Check className="-mt-1 inline-block" />
                <span className="ml-1 mr-base text-[#222223]">{clause.label}</span>
              </Fragment>
            ))}
          </div>
          <Arrow />
        </div>
      </div>

      {/* 保障服务弹窗 */}
      <CustomPopup
        visible={serviceVisible}
        onClose={() => setServiceVisible(false)}
        showHeader={true}
        headTitle="服务保障">
        <div className="p-base-24">
          {safeguardContent?.map((item, index) => (
            <div key={index} className="mb-base-24">
              <p className="font-miSansMedium380 text-lg leading-[1.4] text-black">{item?.title}</p>
              {item?.content ? (
                <RenderHtml
                  contentStyle="text-[#6E6E73] text-[14px] leading-[1.8] mt-[6px]"
                  content={item?.content}
                />
              ) : null}
            </div>
          ))}
        </div>
      </CustomPopup>
    </>
  )
}

export default SafeguardClauses
