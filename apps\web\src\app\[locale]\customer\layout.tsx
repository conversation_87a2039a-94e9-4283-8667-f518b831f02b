import { ReactNode } from 'react'
import { useTranslations } from 'next-intl'

import { JumpToCustomerService } from '@/businessComponents'
import { Breadcrumb, Navigation, ServiceButton } from '@/components'

const Layout = ({ children }: { children: ReactNode }) => {
  const getI18nString = useTranslations('Web')
  return (
    <div className="min-w-[1024px] bg-[#F8F8F9]">
      <div className="max-container mx-auto pb-base-32">
        {/* 面包屑 */}
        <Breadcrumb
          category={{
            uid: '2',
            name: getI18nString('account_center'),
            url_path: 'customer/account',
            url_suffix: '',
            breadcrumbs: [],
          }}
        />
        <div className="my-base-16 flex gap-base-16">
          <Navigation />
          {children}
        </div>

        <div className="fixed bottom-64 right-24 flex flex-col gap-10">
          <JumpToCustomerService
            udeskParams={{
              type: 'order',
              targetId: '',
            }}>
            <ServiceButton isWhite />
          </JumpToCustomerService>
        </div>
      </div>
    </div>
  )
}

export default Layout
