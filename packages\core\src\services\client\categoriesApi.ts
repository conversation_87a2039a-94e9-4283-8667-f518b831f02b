import { GET_CATEGORIES, GET_CATEGORY_BREADCRUMBS, GET_CATEGORY_PRODUCTS } from '../../graphql'
import {
  GetCategoriesQuery,
  GetCategoriesQueryVariables,
  GetCategoryBreadcrumbsQuery,
  GetCategoryBreadcrumbsQueryVariables,
  GetCategoryProductsQuery,
  GetCategoryProductsQueryVariables,
} from '../../graphql/generated/graphql'
import rootApi from '../../utils/rootApi'

/**
 * categories api
 */
const categoriesApi = rootApi.injectEndpoints({
  endpoints: (builder) => ({
    /**
     * 获取全部分类列表
     */
    getCategoriesAll: builder.query<GetCategoriesQuery, GetCategoriesQueryVariables>({
      query: (variables) => ({
        document: GET_CATEGORIES,
        variables,
      }),
      extraOptions: { isAuth: false },
    }),
    /**
     * 获取分类商品列表
     */
    getCategoryProducts: builder.query<GetCategoryProductsQuery, GetCategoryProductsQueryVariables>(
      {
        query: (variables) => ({
          document: GET_CATEGORY_PRODUCTS,
          variables,
        }),
        extraOptions: { isAuth: false },
      },
    ),
    /**
     * 获取分类面包屑数据
     */
    getCategoryBreadcrumbs: builder.query<
      GetCategoryBreadcrumbsQuery,
      GetCategoryBreadcrumbsQueryVariables
    >({
      query: (variables) => ({
        document: GET_CATEGORY_BREADCRUMBS,
        variables,
      }),
      extraOptions: { isAuth: false },
    }),
  }),
})

export const {
  useGetCategoriesAllQuery,
  useLazyGetCategoriesAllQuery,
  useGetCategoryProductsQuery,
  useLazyGetCategoryProductsQuery,
  useGetCategoryBreadcrumbsQuery,
  useLazyGetCategoryBreadcrumbsQuery,
} = categoriesApi

export default categoriesApi
