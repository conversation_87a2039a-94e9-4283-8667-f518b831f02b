import { useMemo } from 'react'

import { GetOrderDetailQuery } from '../graphql/generated/graphql'
import { OrderListItem } from '../typings'

type TOrderData = NonNullable<
  NonNullable<NonNullable<GetOrderDetailQuery>['customer']>['orders']
>['items']

export type TOrderDetailProps = TOrderData[number] | undefined

const useOrderDetail = (orderData: TOrderDetailProps | OrderListItem) => {
  /**
   * 是否是迁移订单
   */
  const isMigrated = useMemo(() => !!orderData?.is_migration, [orderData?.is_migration])

  /**
   * 迁移订单数据
   */
  const migrationOrder = useMemo(() => {
    if (isMigrated && orderData?.migration_data) {
      return orderData?.migration_data
    }
    return null
  }, [isMigrated, orderData])

  /**
   * 迁移订单产品数量
   */
  const migrationProductCount = useMemo(() => {
    let total = 0
    if (isMigrated && migrationOrder?.items?.length) {
      migrationOrder.items.forEach((item) => {
        total += Number(item?.product_qty)
      })
    }
    return total
  }, [isMigrated, migrationOrder])

  /**
   * 迁移订单总价
   */
  const migrationTotalNCoin = useMemo(() => {
    let total = 0
    if (isMigrated && migrationOrder?.items?.length) {
      migrationOrder.items.forEach((item) => {
        const itemTotalNCoin = Number(item?.product_need_npoint) * Number(item?.product_qty)
        total += itemTotalNCoin
      })
    }
    return total
  }, [isMigrated, migrationOrder])

  /**
   * 是否售后单
   */
  const isReturn = useMemo(() => {
    if (isMigrated) {
      return false
    }
    if (orderData && 'total_qty' in orderData) {
      return Number(orderData.total_qty) >= 0
    }
    return false
  }, [orderData, isMigrated])

  /**
   * 是否显示邮寄地址
   */
  const showShippingAddress = useMemo(() => {
    if (orderData && 'display_shipping_address' in orderData) {
      return orderData?.display_shipping_address === true
    }

    return false
  }, [orderData])

  /**
   * 是否邮寄中
   */
  const isTracking = useMemo(() => {
    if (orderData && 'shipment_tracking' in orderData) {
      return Number(orderData?.shipment_tracking?.length) > 0
    }

    return false
  }, [orderData])

  /**
   * 是否含有N币
   */
  const hasNCoin = useMemo(() => {
    if (isMigrated) {
      return true
    }

    if (isReturn && orderData?.total && 'refund_ncoin' in orderData?.total) {
      return Number(orderData?.total?.refund_ncoin) > 0
    } else {
      return (
        Number(orderData?.ncoin_pay?.grand_total) > 0 || Number(orderData?.ncoin_pay?.subtotal) > 0
      )
    }
  }, [isReturn, orderData, isMigrated])

  /**
   * 是否纯N币订单
   */
  const isOnlyNCoin = useMemo(() => {
    if (isMigrated) {
      return true
    }
    if (isReturn && orderData?.total && 'refund_ncoin' in orderData?.total) {
      return hasNCoin && Number(orderData?.total?.refund_total?.value) <= 0
    } else {
      return hasNCoin && Number(orderData?.total?.grand_total?.value) <= 0
    }
  }, [isMigrated, isReturn, hasNCoin, orderData?.total])

  /**
   * 是否0元购
   */
  const isFree = useMemo(() => {
    if (isMigrated) {
      return false
    }
    return !hasNCoin && orderData?.total?.grand_total?.value === 0
  }, [hasNCoin, orderData?.total, isMigrated])

  /**
   * 是否全部为虚拟产品
   */
  const isAllVirtualProduct = useMemo(() => {
    if (isMigrated) {
      return true
    }

    if (orderData && 'display_shipping_address' in orderData) {
      return orderData?.items?.every(
        (item) => item?.product_type === 'virtual' || item?.product_type === 'bundle',
      )
    }

    return false
  }, [orderData, isMigrated])

  /**
   * 是否可分批退货退款
   */
  const isReturnInBatches = useMemo(() => {
    if (orderData && 'requisition_all' in orderData) {
      return orderData?.requisition_all !== true
    }

    return false
  }, [orderData])

  /**
   * 是否待付款
   */
  const isWaitPay = useMemo(() => {
    if (isMigrated) {
      return false
    }
    return (
      orderData?.status_code === 'pending' &&
      Number(orderData?.payment_time_out || 0) > new Date().getTime() / 1000
    )
  }, [orderData?.status_code, orderData?.payment_time_out, isMigrated])

  /**
   * 是否未付款
   */
  const isUnPaid = useMemo(
    () => orderData?.status_code === 'pending' || orderData?.status_code === 'canceled',
    [orderData?.status_code],
  )

  /**
   * 是否有发票
   */
  const hasInvoice = useMemo(() => {
    if (isMigrated) {
      return false
    }
    return !!orderData?.invoice_info?.zlink
  }, [orderData?.invoice_info, isMigrated])

  /**
   * 是否是音效/皮肤产品
   */
  const isSkinSound = useMemo((): boolean => {
    if (isMigrated) {
      return false
    }
    return (
      orderData?.items?.some((item) => {
        // 检查 item 是否有 digital_info 属性
        if (item && 'digital_info' in item && item.digital_info?.sn_code) {
          return true
        }
        return false
      }) || false
    )
  }, [orderData, isMigrated])

  // 总价
  const totalAmount = useMemo(() => {
    return orderData?.total?.grand_total || { value: 0, currencySymbol: '¥' }
  }, [orderData])

  // N币支付总价
  const nCoin = useMemo(() => {
    return Number(orderData?.ncoin_pay?.grand_total || 0)
  }, [orderData])

  // 支付方式
  const paymentMethodCode = useMemo(() => {
    return orderData?.payment_methods?.[0]?.type
  }, [orderData])

  /**
   * 申请退后时，是否展示用户地址
   */
  const isShowAddressByShippingMethod = useMemo(() => {
    if (orderData && 'delivery_method' in orderData) {
      return orderData?.delivery_method?.method === '1'
    }

    return false
  }, [orderData])

  return {
    isMigrated,
    migrationOrder,
    migrationProductCount,
    migrationTotalNCoin,
    isReturn,
    showShippingAddress,
    isTracking,
    hasNCoin,
    isOnlyNCoin,
    isFree,
    isAllVirtualProduct,
    isReturnInBatches,
    isWaitPay,
    isUnPaid,
    hasInvoice,
    isSkinSound,
    totalAmount,
    nCoin,
    paymentMethodCode,
    isShowAddressByShippingMethod,
  }
}

export default useOrderDetail
