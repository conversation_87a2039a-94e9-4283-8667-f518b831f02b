'use client'
import { useCallback, useEffect, useRef, useState } from 'react'
import type { StoreListItem, TBaseComponentProps } from '@ninebot/core'
import {
  TRACK_EVENT,
  useLazyGetProductSoundQuery,
  useLoadingContext,
  useToastContext,
  useVolcAnalytics,
} from '@ninebot/core'
import type {
  GetProductSoundQuery,
  GetProductsQuery,
} from '@ninebot/core/src/graphql/generated/graphql'
import { resolveCatchMessage, sleep, TCatchMessage } from '@ninebot/core/src/utils/util'

import { StoreSelectorModal } from '@/businessComponents'
import { RecommendProducts } from '@/components'
import ProductBreadcrumb from '@/components/business/ProductBreadcrumb'
import { useBreadcrumbData } from '@/hooks/useBreadcrumbData'
import { useSimpleScrollLock } from '@/hooks/useScrollLock'
import { calculateViewportHeight, useViewportHeight } from '@/hooks/useViewportHeight'

import ProductActionButtons from './components/ProductActionBttons'
import { ProductProvider, useProduct } from './context/ProductContext'
import {
  FloatingButtons,
  ProductEmpty,
  ProductGallery,
  ProductInfo,
  ProductSkeleton,
  ProductSkinSound,
  ProductTabs,
} from './components'
type Products = NonNullable<GetProductsQuery['products']>
type ProductItems = NonNullable<Products['items']>
// 产品类型
type ProductItem = NonNullable<ProductItems[number]>
type soundListType = NonNullable<GetProductSoundQuery['digital_product_audio']>[number] & {
  id: number
}
export type TProductPageProps = TBaseComponentProps<{
  data: ProductItem
}>

const ProductContent = ({ productDetails }: { productDetails: ProductItem | null }) => {
  const {
    isLoading,
    termsOfSale,
    showProductRecommend,
    productStatus,
    doorVisible,
    setDoorVisible,
    selectStore,
    setSelectStore,
    setVisibleAddCartPop,
    deliveryMethodPickup,
    onAddToCart,
  } = useProduct() as {
    isLoading: boolean
    termsOfSale: {
      value: string
    }
    showProductRecommend: boolean
    productStatus: {
      selectOptionName: string
      serviceItem: {
        sku: string
      }[]
      id: string
    }
    doorVisible: boolean
    setDoorVisible: (visible: boolean) => void
    selectStore: StoreListItem | null
    setSelectStore: (store: StoreListItem | null) => void
    setVisibleAddCartPop: (visible: boolean) => void
    deliveryMethodPickup: boolean
    onAddToCart: (type: number) => void
  }

  // 获取真实的视口高度，解决移动端兼容性问题
  const viewportHeight = useViewportHeight()

  // 防止滚动穿透的 ref
  const scrollLockRef = useSimpleScrollLock()

  // 添加对产品图片容器的引用
  const galleryContainerRef = useRef<HTMLDivElement>(null)
  // 添加对产品信息容器的引用
  const infoContainerRef = useRef<HTMLDivElement>(null)
  // 添加对右侧信息区域的引用
  const stickyInfoRef = useRef<HTMLDivElement>(null)
  // 添加一个观察目标元素的引用（用于检测sticky状态）
  const stickyObserverRef = useRef<HTMLDivElement>(null)
  // 添加推荐区域的引用，用于TabHeader的滚动定位
  const recommendRef = useRef<HTMLDivElement>(null)
  // 添加TabHeader吸顶状态管理
  const [isTabHeaderSticky, setIsTabHeaderSticky] = useState(false)
  // 添加内容区域监听目标的引用
  const contentObserverRef = useRef<HTMLDivElement>(null)

  // 添加皮肤音效弹窗状态
  const [soundPopVisible, setSoundPopVisible] = useState(false)
  const [soundList, setSoundList] = useState<soundListType[]>([])
  const [getProductSound] = useLazyGetProductSoundQuery()
  const loading = useLoadingContext()
  const toast = useToastContext()

  // 面包屑数据
  const { breadcrumbItems } = useBreadcrumbData(
    productDetails
      ? {
          uid: productDetails.uid,
          name: productDetails.name || '',
        }
      : { uid: '', name: '' },
  )

  // 调试面包屑数据
  // useEffect(() => {
  //   console.log('ProductPage Debug - Breadcrumb items:', breadcrumbItems)
  // }, [breadcrumbItems])

  // 添加sticky状态
  const [isSticky, setIsSticky] = useState(false)
  // 根据sku获取对应的音效
  const getSound = useCallback(
    async (productSku: string) => {
      try {
        const res = await getProductSound({
          sku: productSku,
        }).unwrap()
        loading.hide()
        await sleep(500)
        if (res?.digital_product_audio?.length) {
          const newList = res.digital_product_audio
            ?.filter((item) => item?.display)
            ?.map((item, index) => ({
              id: index + 1,
              ...item,
            }))
          setSoundList(newList as unknown as soundListType[])
        }
      } catch (error) {
        await sleep(500)
        loading.hide()
        toast.show({
          icon: 'fail',
          content: resolveCatchMessage(error as TCatchMessage) as string,
        })
      }
    },
    [getProductSound, loading, toast],
  )

  // 获取皮肤音效列表
  useEffect(() => {
    if (productDetails?.digital_has_audio && productDetails?.sku) {
      getSound(productDetails.sku)
    } else {
      setSoundList([])
    }
  }, [productDetails, getSound])

  // 使用IntersectionObserver检测右侧信息区域是否sticky固定
  useEffect(() => {
    if (!stickyObserverRef.current) return

    const observer = new IntersectionObserver(
      (entries) => {
        const entry = entries[0]
        // 当观察目标元素不可见时，说明右侧信息区域已经进入sticky状态
        setIsSticky(!entry.isIntersecting)
      },
      {
        // 设置rootMargin为-92px，这样当元素距离视口顶部92px时就会触发
        rootMargin: '-92px 0px 0px 0px',
        threshold: 0,
      },
    )

    observer.observe(stickyObserverRef.current)

    return () => {
      observer.disconnect()
    }
  }, [])

  // 使用IntersectionObserver检测TabHeader是否应该吸顶
  useEffect(() => {
    if (!contentObserverRef.current) return

    const observer = new IntersectionObserver(
      (entries) => {
        const entry = entries[0]
        // 当内容区域观察目标元素离开视口时，TabHeader应该吸顶
        setIsTabHeaderSticky(!entry.isIntersecting)
      },
      {
        // 设置rootMargin，当元素距离视口顶部一定距离时触发
        rootMargin: '0px 0px -100% 0px',
        threshold: 0,
      },
    )

    observer.observe(contentObserverRef.current)

    return () => {
      observer.disconnect()
    }
  }, [])

  // 如果产品数据仍在加载中，显示骨架屏
  if (isLoading) {
    return <ProductSkeleton />
  }

  return (
    <div
      className="max-container relative"
      style={{ minHeight: calculateViewportHeight(56, viewportHeight) }}>
      {/* 滚动时显示的覆盖层 */}
      <div
        className={`fixed left-0 top-0 z-40 h-[92px] w-full bg-white transition-opacity duration-0 ${
          isSticky ? 'opacity-100' : 'pointer-events-none opacity-0'
        }`}
      />

      {productDetails ? (
        <>
          {/* 面包屑*/}
          <div className="mb-base-12">
            <ProductBreadcrumb items={breadcrumbItems} />
          </div>

          {/* 主要内容区域 */}
          <div className="flex gap-16 pb-16">
            {/* 左侧内容区域 - 9/14比例 */}
            <div className="min-w-0 flex-[9]">
              {/* 商品图片展示 */}
              <div ref={galleryContainerRef} className="mb-16">
                <ProductGallery setSoundPopVisible={setSoundPopVisible} />
              </div>

              {/* TabHeader吸顶监听目标 - 放在ProductTabs之前 */}
              <div ref={contentObserverRef} className="h-0" />

              {/* 商品详情/评价/售后服务/条款说明 切换区域 */}
              <div className="mb-16">
                <ProductTabs
                  productData={productDetails}
                  termsOfSale={termsOfSale}
                  recommendRef={recommendRef}
                  isSticky={isTabHeaderSticky}
                  showRecommendTab={showProductRecommend}
                />
              </div>
            </div>

            {/* 右侧固定信息区域 - 5/14比例，使用sticky定位 */}
            <div className="min-w-[434px] flex-[5]">
              {/* IntersectionObserver观察目标元素 - 用于检测sticky状态 */}
              <div ref={stickyObserverRef} className="h-0" />

              <div
                ref={stickyInfoRef}
                className="sticky top-[92px] flex flex-col bg-white transition-all duration-300"
                style={{
                  height: isSticky
                    ? calculateViewportHeight(92, viewportHeight)
                    : calculateViewportHeight(140, viewportHeight),
                }}>
                {/* 固定在顶部的关键信息区域 */}
                <div className="flex-shrink-0">
                  <ProductInfo showFixedContent={true} />
                </div>

                {/* 可滚动的其他内容区域 */}
                <div
                  ref={scrollLockRef}
                  className="scroll-hide scroll-container min-h-0 flex-1 overflow-y-auto">
                  <div ref={infoContainerRef}>
                    <ProductInfo showFixedContent={false} />
                  </div>
                </div>

                {/* 固定在底部的按钮区域 */}
                <div className="z-50 flex-shrink-0 bg-white px-0 py-base-24">
                  {productDetails?.__typename !== 'BundleProduct' && <ProductActionButtons />}
                </div>
              </div>
            </div>
          </div>

          {/* 弹窗组件 */}
          {deliveryMethodPickup && (
            <StoreSelectorModal
              doorVisible={doorVisible}
              setDoorVisible={setDoorVisible}
              selectStore={selectStore}
              setSelectStore={setSelectStore}
              setVisibleAddCartPop={setVisibleAddCartPop}
              productId={productStatus.id}
              onAddToCart={onAddToCart}
            />
          )}
          <ProductSkinSound
            soundList={soundList || []}
            visible={soundPopVisible}
            setVisible={setSoundPopVisible}
          />
        </>
      ) : (
        <ProductEmpty />
      )}

      {/* 为你推荐 */}
      {showProductRecommend && (
        <div ref={recommendRef} data-tab-id="recommend" className="mt-32">
          <RecommendProducts sku={productDetails?.sku || ''} isProductDetailPage={true} />
        </div>
      )}

      <FloatingButtons />
    </div>
  )
}

/**
 * 产品详情页面
 * 该页面需要SSR渲染，只有需要客户操作的区域使用CSR渲染
 */
const ProductPage = ({ data }: TProductPageProps) => {
  // const { __typename, sku, name } = data
  const { reportEvent } = useVolcAnalytics()
  // 添加loading状态管理
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    if (data) {
      /**
       * 埋点：点击商品详情页
       */
      reportEvent(TRACK_EVENT.shop_commodity_details_exposure, {
        product_id: data?.uid,
        sku_id: data?.sku,
        product_name: data?.name,
      })
      setIsLoading(false)
    }
  }, [data, reportEvent])

  if (isLoading) {
    return <ProductSkeleton />
  }

  return (
    <ProductProvider productDetails={data}>
      <ProductContent productDetails={data} />
    </ProductProvider>
  )
}

export default ProductPage
